# Generated by Django 5.1.5 on 2025-07-19 07:00

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0003_merge_20250719_0659'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='messagereaction',
            name='unique_message_user_reaction',
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='message_type',
            field=models.CharField(choices=[('text', 'Text'), ('file', 'File'), ('image', 'Image'), ('voice', 'Voice Message'), ('video', 'Video'), ('location', 'Location'), ('system', 'System Message')], default='text', max_length=10),
        ),
        migrations.AlterUniqueTogether(
            name='messagereaction',
            unique_together={('message', 'user', 'reaction')},
        ),
    ]
