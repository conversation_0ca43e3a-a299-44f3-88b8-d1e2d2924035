{% extends "partials/base.html" %}
{% load static %}
{% load humanize %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-wallet mr-2"></i>Wholesale Wallet Transaction History - {{ customer.name }}</h2>
        <div>
            <a href="{% url 'wholesale:wholesale_customers' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Wholesale Customers
            </a>
            {% comment %} <a href="{% url 'wholesale:wholesale_customer_wallet_details' customer.id %}" class="btn btn-primary">
                <i class="fas fa-wallet"></i> Wallet Details
            </a> {% endcomment %}
        </div>
    </div>

    <!-- Wallet Balance Card -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Current Balance</h5>
                    <h3>₦{{ wallet_balance|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Deposits</h5>
                    <h3>₦{{ totals.deposit|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Purchases</h5>
                    <h3>₦{{ totals.purchase|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Refunds</h5>
                    <h3>₦{{ totals.refund|floatformat:2|intcomma }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter"></i> Filter Transactions</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-3">
                    <label for="transaction_type">Transaction Type:</label>
                    <select name="transaction_type" id="transaction_type" class="form-control">
                        <option value="">All Types</option>
                        {% for value, label in transaction_types %}
                        <option value="{{ value }}" {% if filters.transaction_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from">From Date:</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" 
                           value="{{ filters.date_from|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to">To Date:</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" 
                           value="{{ filters.date_to|default:'' }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{% url 'store:wholesale_wallet_transaction_history' customer.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Transaction History ({{ transactions.count }} transactions)</h5>
        </div>
        <div class="card-body">
            {% if transactions %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"M d, Y H:i" }}</td>
                            <td>
                                <span class="badge 
                                    {% if transaction.transaction_type == 'deposit' %}badge-success
                                    {% elif transaction.transaction_type == 'purchase' %}badge-danger
                                    {% elif transaction.transaction_type == 'refund' %}badge-info
                                    {% else %}badge-warning{% endif %}">
                                    {{ transaction.get_transaction_type_display }}
                                </span>
                            </td>
                            <td>
                                <strong class="
                                    {% if transaction.transaction_type == 'deposit' or transaction.transaction_type == 'refund' %}text-success
                                    {% else %}text-danger{% endif %}">
                                    {% if transaction.transaction_type == 'deposit' or transaction.transaction_type == 'refund' %}+{% else %}-{% endif %}₦{{ transaction.amount|floatformat:2|intcomma }}
                                </strong>
                            </td>
                            <td>{{ transaction.description|default:"No description" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">No wallet transactions match your current filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
