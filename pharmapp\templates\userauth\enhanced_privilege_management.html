{% extends "partials/base.html" %}
{% load static %}

{% block content %}
<div class="privilege-management-container">
    <!-- Header Section -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-user-shield"></i> Advanced Privilege Management
                </h1>
                <p class="page-description">Comprehensive user permission and role management system</p>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="openBulkOperationsModal()">
                        <i class="fas fa-users-cog"></i> Bulk Operations
                    </button>
                    <button class="btn btn-info" onclick="exportPermissions()">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <button class="btn btn-success" onclick="openPermissionTemplateModal()">
                        <i class="fas fa-plus"></i> Create Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-users-count">{{ total_users }}</h3>
                    <p>Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-content">
                    <h3 id="active-users-count">0</h3>
                    <p>Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-key"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-permissions-count">{{ total_permissions }}</h3>
                    <p>Available Permissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-user-tag"></i>
                </div>
                <div class="stat-content">
                    <h3 id="active-roles-count">{{ active_roles }}</h3>
                    <p>Active Roles</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="granted-permissions-count">0</h3>
                    <p>Granted Permissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-icon bg-danger">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="revoked-permissions-count">0</h3>
                    <p>Revoked Permissions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="row">
        <!-- User Selection Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> User Management
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="mb-3">
                        <div class="input-group">
                            <input type="text" class="form-control" id="user-search" placeholder="Search users...">
                            <button class="btn btn-outline-secondary" onclick="searchUsers()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Role Filter -->
                    <div class="mb-3">
                        <select class="form-select" id="role-filter" onchange="filterUsersByRole()">
                            <option value="">All Roles</option>
                            <option value="Admin">Admin</option>
                            <option value="Manager">Manager</option>
                            <option value="Pharmacist">Pharmacist</option>
                            <option value="Pharm-Tech">Pharm-Tech</option>
                            <option value="Salesperson">Salesperson</option>
                        </select>
                    </div>

                    <!-- User List -->
                    <div class="user-list" id="user-list">
                        {% for user in users %}
                        <div class="user-item" data-user-id="{{ user.id }}" data-role="{{ user.profile.user_type }}" onclick="selectUser({{ user.id }})">
                            <div class="user-avatar">
                                {% if user.profile.image %}
                                    <img src="{{ user.profile.image.url }}" alt="{{ user.username }}">
                                {% else %}
                                    <div class="avatar-placeholder">{{ user.username|first|upper }}</div>
                                {% endif %}
                            </div>
                            <div class="user-info">
                                <h6>{{ user.profile.full_name|default:user.username }}</h6>
                                <small class="text-muted">{{ user.profile.user_type }}</small>
                                <div class="user-status">
                                    <span class="badge bg-{{ user.is_active|yesno:'success,secondary' }}">
                                        {{ user.is_active|yesno:'Active,Inactive' }}
                                    </span>
                                </div>
                            </div>
                            <div class="user-actions">
                                <input type="checkbox" class="form-check-input user-checkbox" value="{{ user.id }}">
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Bulk Actions -->
                    <div class="bulk-actions mt-3" id="bulk-actions" style="display: none;">
                        <div class="btn-group w-100">
                            <button class="btn btn-sm btn-primary" onclick="bulkApplyRole()">
                                <i class="fas fa-user-tag"></i> Apply Role
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="bulkToggleStatus()">
                                <i class="fas fa-toggle-on"></i> Toggle Status
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="bulkRemovePermissions()">
                                <i class="fas fa-minus-circle"></i> Remove Perms
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Management Panel -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-key"></i> Permission Management
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="showPermissionMatrix()">
                                <i class="fas fa-table"></i> Matrix View
                            </button>
                            <button class="btn btn-outline-success" onclick="showAuditTrail()">
                                <i class="fas fa-history"></i> Audit Trail
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Selected User Info -->
                    <div class="selected-user-info" id="selected-user-info" style="display: none;">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <div class="selected-user-avatar me-3">
                                    <div class="avatar-placeholder" id="selected-user-avatar"></div>
                                </div>
                                <div>
                                    <h6 class="mb-1" id="selected-user-name">No user selected</h6>
                                    <small class="text-muted" id="selected-user-role">Select a user to manage permissions</small>
                                </div>
                                <div class="ms-auto">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetails()">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permission Categories -->
                    <div class="permission-categories" id="permission-categories">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="permission-category">
                                    <h6 class="category-title">
                                        <i class="fas fa-user-shield"></i> User Management
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('user-management', true)">
                                            <i class="fas fa-check-square"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('user-management', false)">
                                            <i class="fas fa-square"></i>
                                        </button>
                                    </h6>
                                    <div class="permission-list" id="user-management-permissions">
                                        <div class="permission-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="form-check flex-grow-1">
                                                    <input class="form-check-input" type="checkbox" id="manage_users">
                                                    <label class="form-check-label" for="manage_users">
                                                        Manage Users
                                                        <small class="text-muted d-block">Create, edit, and delete user accounts</small>
                                                    </label>
                                                </div>
                                                <div class="permission-actions">
                                                    <button class="btn btn-sm btn-outline-success" onclick="grantPermission('manage_users')" title="Grant Permission">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="revokePermission('manage_users')" title="Revoke Permission">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="form-check flex-grow-1">
                                                    <input class="form-check-input" type="checkbox" id="edit_user_profiles">
                                                    <label class="form-check-label" for="edit_user_profiles">
                                                        Edit User Profiles
                                                        <small class="text-muted d-block">Modify user profile information</small>
                                                    </label>
                                                </div>
                                                <div class="permission-actions">
                                                    <button class="btn btn-sm btn-outline-success" onclick="grantPermission('edit_user_profiles')" title="Grant Permission">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="revokePermission('edit_user_profiles')" title="Revoke Permission">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="permission-category">
                                    <h6 class="category-title">
                                        <i class="fas fa-pills"></i> Inventory Management
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('inventory-management', true)">
                                            <i class="fas fa-check-square"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('inventory-management', false)">
                                            <i class="fas fa-square"></i>
                                        </button>
                                    </h6>
                                    <div class="permission-list" id="inventory-management-permissions">
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="manage_inventory">
                                                <label class="form-check-label" for="manage_inventory">
                                                    Manage Inventory
                                                    <small class="text-muted d-block">Add, edit, and remove inventory items</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perform_stock_check">
                                                <label class="form-check-label" for="perform_stock_check">
                                                    Perform Stock Check
                                                    <small class="text-muted d-block">Conduct inventory audits and stock checks</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="transfer_stock">
                                                <label class="form-check-label" for="transfer_stock">
                                                    Transfer Stock
                                                    <small class="text-muted d-block">Move inventory between locations</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="permission-category">
                                    <h6 class="category-title">
                                        <i class="fas fa-cash-register"></i> Sales & Transactions
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('sales-management', true)">
                                            <i class="fas fa-check-square"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('sales-management', false)">
                                            <i class="fas fa-square"></i>
                                        </button>
                                    </h6>
                                    <div class="permission-list" id="sales-management-permissions">
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="process_sales">
                                                <label class="form-check-label" for="process_sales">
                                                    Process Sales
                                                    <small class="text-muted d-block">Handle customer transactions and sales</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="process_returns">
                                                <label class="form-check-label" for="process_returns">
                                                    Process Returns
                                                    <small class="text-muted d-block">Handle product returns and refunds</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="process_split_payments">
                                                <label class="form-check-label" for="process_split_payments">
                                                    Process Split Payments
                                                    <small class="text-muted d-block">Handle multiple payment methods</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="permission-category">
                                    <h6 class="category-title">
                                        <i class="fas fa-chart-bar"></i> Reports & Analytics
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('reports-management', true)">
                                            <i class="fas fa-check-square"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link" onclick="toggleCategoryPermissions('reports-management', false)">
                                            <i class="fas fa-square"></i>
                                        </button>
                                    </h6>
                                    <div class="permission-list" id="reports-management-permissions">
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="view_reports">
                                                <label class="form-check-label" for="view_reports">
                                                    View Reports
                                                    <small class="text-muted d-block">Access system reports and analytics</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="view_financial_reports">
                                                <label class="form-check-label" for="view_financial_reports">
                                                    View Financial Reports
                                                    <small class="text-muted d-block">Access financial data and reports</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="permission-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="view_activity_logs">
                                                <label class="form-check-label" for="view_activity_logs">
                                                    View Activity Logs
                                                    <small class="text-muted d-block">Monitor system activity and user actions</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="permission-actions mt-4" id="permission-actions" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" onclick="savePermissions()">
                                        <i class="fas fa-save"></i> Save Changes
                                    </button>
                                    <button class="btn btn-secondary" onclick="resetPermissions()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group w-100">
                                    <button class="btn btn-info" onclick="applyRoleTemplate()">
                                        <i class="fas fa-magic"></i> Apply Template
                                    </button>
                                    <button class="btn btn-warning" onclick="copyPermissions()">
                                        <i class="fas fa-copy"></i> Copy From User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current User Permissions Summary -->
    <div class="row mt-4" id="permissions-summary-section" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check"></i> Current User Permissions Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div id="current-permissions-display">
                        <p class="text-muted text-center">Select a user to view their current permissions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Operations Modal -->
<div class="modal fade" id="bulkOperationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Operations</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Selected Users (<span id="selected-users-count">0</span>)</h6>
                        <div id="selected-users-list" class="selected-users-list">
                            <!-- Selected users will be displayed here -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Bulk Actions</h6>
                        <div class="bulk-action-options">
                            <div class="mb-3">
                                <label class="form-label">Apply Role Template</label>
                                <select class="form-select" id="bulk-role-template">
                                    <option value="">Select Role</option>
                                    <option value="Admin">Admin</option>
                                    <option value="Manager">Manager</option>
                                    <option value="Pharmacist">Pharmacist</option>
                                    <option value="Pharm-Tech">Pharm-Tech</option>
                                    <option value="Salesperson">Salesperson</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status Change</label>
                                <select class="form-select" id="bulk-status-change">
                                    <option value="">No Change</option>
                                    <option value="activate">Activate Users</option>
                                    <option value="deactivate">Deactivate Users</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Add Permissions</label>
                                <div class="permission-checkboxes">
                                    <!-- Permission checkboxes will be generated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkOperations()">Apply Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Permission Matrix Modal -->
<div class="modal fade" id="permissionMatrixModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Permission Matrix</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered permission-matrix-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <!-- Permission columns will be generated dynamically -->
                            </tr>
                        </thead>
                        <tbody id="permission-matrix-body">
                            <!-- Matrix data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.privilege-management-container {
    padding: 20px;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.page-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 5px;
}

.page-description {
    color: #6c757d;
    margin-bottom: 0;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.user-list {
    max-height: 500px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.user-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.user-item.selected {
    background-color: #e3f2fd;
    border-color: #007bff;
}

.user-avatar {
    width: 40px;
    height: 40px;
    margin-right: 12px;
}

.user-avatar img, .avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.user-info {
    flex: 1;
}

.user-info h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.user-status {
    margin-top: 4px;
}

.permission-category {
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
}

.category-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.permission-item {
    margin-bottom: 12px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.permission-item:hover {
    background-color: #f8f9fa;
}

.permission-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.permission-item:hover .permission-actions {
    opacity: 1;
}

.permission-actions .btn {
    margin-left: 2px;
    padding: 2px 6px;
    font-size: 0.75rem;
}

.permission-actions .btn:hover {
    transform: scale(1.1);
}

/* Permissions Summary Styles */
.user-permissions-summary {
    padding: 15px;
}

.permission-category {
    border-left: 3px solid #dee2e6;
    padding-left: 15px;
    margin-bottom: 20px;
}

.permission-category h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

.permission-badges {
    line-height: 2;
}

.permission-badges .badge {
    font-size: 0.8rem;
    padding: 4px 8px;
}

.permission-summary-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.stat-item h5 {
    margin-bottom: 5px;
    font-weight: 700;
}

.stat-item small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

.form-check-label small {
    font-weight: normal;
    color: #6c757d;
}

.selected-user-info {
    margin-bottom: 20px;
}

.selected-user-avatar {
    width: 50px;
    height: 50px;
}

.selected-user-avatar .avatar-placeholder {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
}

.permission-matrix-table {
    font-size: 0.85rem;
}

.permission-matrix-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.permission-matrix-table td {
    text-align: center;
    vertical-align: middle;
}

.selected-users-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
}

.bulk-action-options .permission-checkboxes {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
}

@media (max-width: 768px) {
    .privilege-management-container {
        padding: 10px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .permission-categories .col-md-6 {
        margin-bottom: 20px;
    }
}
</style>

<script>
// Enhanced Privilege Management JavaScript
let selectedUserId = null;
let selectedUsers = new Set();
let currentUserPermissions = {};
let allPermissions = {};

// Initialize the system
document.addEventListener('DOMContentLoaded', function() {
    initializePrivilegeManagement();
    loadPermissionData();
    bindEventListeners();
});

function initializePrivilegeManagement() {
    // Load initial data
    updateStatistics();
    setupUserCheckboxes();
}

function bindEventListeners() {
    // User search
    document.getElementById('user-search').addEventListener('input', debounce(searchUsers, 300));
    
    // User checkboxes for bulk operations
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleUserSelection);
    });
}

// More JavaScript functions will be added in the next file
</script>
{% endblock %}
