from django import template

register = template.Library()

@register.filter
def can_view_all_users_dispensing(user):
    """
    Template filter to check if a user can view all users' dispensing data.
    Returns True for superusers, staff, admins, and managers.
    
    Usage in templates: {% if user|can_view_all_users_dispensing %}
    """
    return (
        user.is_superuser or 
        user.is_staff or
        (hasattr(user, 'profile') and 
         user.profile and 
         user.profile.user_type in ['Admin', 'Manager'])
    )

@register.simple_tag
def user_can_view_all_dispensing(user):
    """
    Template tag to check if a user can view all users' dispensing data.
    
    Usage in templates: {% user_can_view_all_dispensing user as can_view_all %}
    """
    return can_view_all_users_dispensing(user)
