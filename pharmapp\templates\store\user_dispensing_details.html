{% extends "partials/base.html" %}
{% load static %}
{% load humanize %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-list-alt mr-2"></i>
            {% if target_user %}
                {% if target_user == user and not can_view_all_users %}
                    My Dispensing Details
                {% else %}
                    {{ target_user.get_full_name|default:target_user.username }}'s Dispensing Details
                {% endif %}
            {% else %}
                {% if can_view_all_users %}
                    All Users Dispensing Details
                {% else %}
                    My Dispensing Details
                {% endif %}
            {% endif %}
        </h2>
        <div>
            <a href="{% url 'store:user_dispensing_summary' %}" class="btn btn-secondary">
                <i class="fas fa-chart-bar"></i> Back to Summary
            </a>
            <a href="{% url 'store:dispensing_log' %}" class="btn btn-info">
                <i class="fas fa-list"></i> Dispensing Log
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter"></i> Filters</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                {% if target_user %}
                    <input type="hidden" name="user_id" value="{{ target_user.id }}">
                {% elif can_view_all_users %}
                    <div class="col-md-3">
                        <label for="user_id">User:</label>
                        <select name="user_id" id="user_id" class="form-control">
                            <option value="">All Users</option>
                            {% for user_option in all_users %}
                                <option value="{{ user_option.id }}" {% if filters.user_id == user_option.id|stringformat:"s" %}selected{% endif %}>
                                    {{ user_option.get_full_name|default:user_option.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                {% else %}
                    <div class="col-md-3">
                        <label for="user_info">Viewing:</label>
                        <input type="text" class="form-control" value="My Dispensing Details" readonly>
                        <input type="hidden" name="user_id" value="{{ user.id }}">
                    </div>
                {% endif %}
                <div class="col-md-2">
                    <label for="status">Status:</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if filters.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from">From Date:</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" 
                           value="{{ filters.date_from|default:'' }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to">To Date:</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" 
                           value="{{ filters.date_to|default:'' }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{% url 'store:user_dispensing_details' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Detailed Dispensing Table -->
    <div class="card">
        <div class="card-header">
            <h5>
                <i class="fas fa-table"></i> 
                Detailed Dispensing Records
                {% if logs %}
                    <span class="badge badge-primary ml-2">{{ logs.count }} records</span>
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            {% if logs %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Date & Time</th>
                            <th>User</th>
                            <th>Item Name</th>
                            <th>Brand</th>
                            <th>Unit</th>
                            <th>Quantity</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Returns Info</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>
                                <small class="text-muted">{{ log.created_at|date:"M d, Y" }}</small><br>
                                <small class="text-muted">{{ log.created_at|time:"H:i" }}</small>
                            </td>
                            <td>
                                <strong>{{ log.user.get_full_name|default:log.user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ log.user.username }}</small>
                            </td>
                            <td><strong>{{ log.name }}</strong></td>
                            <td>{{ log.brand|default:"N/A" }}</td>
                            <td>{{ log.unit|default:"N/A" }}</td>
                            <td>{{ log.quantity|floatformat:2 }}</td>
                            <td>₦{{ log.amount|floatformat:2|intcomma }}</td>
                            <td>
                                <span class="badge {% if log.status == 'Dispensed' %}badge-success{% elif log.status == 'Returned' %}badge-danger{% else %}badge-warning{% endif %}">
                                    {{ log.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% with return_info=log.return_summary %}
                                    {% if return_info.return_type == 'fully_returned' %}
                                        <div class="text-danger">
                                            <i class="fas fa-undo"></i>
                                            <small>{{ return_info.status_display }}</small>
                                            <br><small class="text-muted">100% returned</small>
                                        </div>
                                    {% elif return_info.return_type == 'partially_returned' %}
                                        <div class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <small>{{ return_info.status_display }}</small>
                                        </div>
                                    {% elif return_info.return_type == 'separate_returns' %}
                                        <div class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <small>{{ return_info.status_display }}</small>
                                            <br><small class="text-muted">{{ return_info.return_percentage|floatformat:1 }}% returned</small>
                                        </div>
                                    {% elif return_info.return_type == 'no_returns' %}
                                        <span class="text-muted"><small>{{ return_info.status_display }}</small></span>
                                    {% else %}
                                        <span class="text-muted"><small>{{ return_info.status_display }}</small></span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No dispensing records found</h5>
                <p class="text-muted">No dispensing records match your current filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
